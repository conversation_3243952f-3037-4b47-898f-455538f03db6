import { defineStore } from 'pinia'
import { defaultState, getLocalState, setLocalState } from './helper'
import { route, router } from '@/router'
import { t } from '@/locales'

export const useChatStore = defineStore('chat-store', {
  state: (): Chat.ChatState => getLocalState(),

  getters: {
    getChatHistoryByCurrentActive(state: Chat.ChatState) {
      const index = state.history.findIndex(item => item.conversationId === state.active)
      if (index !== -1) return state.history[index]
      return null
    },

    getChatByConversationId(state: Chat.ChatState) {
      return (conversationId?: string) => {
        if (conversationId)
          return state.chat.find(item => item.conversationId === conversationId)?.data ?? []
        return state.chat.find(item => item.conversationId === state.active)?.data ?? []
      }
    },
  },

  actions: {
    setUsingContext(context: boolean) {
      this.usingContext = context
      this.recordState()
    },

    setUseSystemMessage(useSystemMessage: boolean) {
      this.useSystemMessage = useSystemMessage
      this.recordState()
    },

    setModelId(modelId: number) {
      this.modelId = modelId
      this.recordState()
    },

    addHistory(history: Chat.History, chatData: Chat.Chat[] = []) {
      this.history.unshift(history)
      this.chat.unshift({ conversationId: history.conversationId, data: chatData })
      this.active = history.conversationId
      this.reloadRoute(history.conversationId)
    },

    updateHistory(conversationId: string, edit: Partial<Chat.History>) {
      const index = this.history.findIndex(item => item.conversationId === conversationId)
      if (index !== -1) {
        this.history[index] = { ...this.history[index], ...edit }
        this.recordState()
      }
    },

    async deleteHistory(index: number) {
      // 删除前检查是否是当前活跃会话
      const isActiveChat = this.history[index]?.conversationId === this.active

      this.history.splice(index, 1)
      this.chat.splice(index, 1)

      if (this.history.length === 0) {
        this.active = null
        this.reloadRoute()
        return
      }

      // resolved 删除的这条跟目前活跃的会话id一致，则需要重新设置活跃会话
      if (isActiveChat) {
        if (index > 0 && index <= this.history.length) {
          const conversationId = this.history[index - 1].conversationId
          this.active = conversationId
          this.reloadRoute(conversationId)
          return
        }

        if (index === 0) {
          if (this.history.length > 0) {
            const conversationId = this.history[0].conversationId
            this.active = conversationId
            this.reloadRoute(conversationId)
          }
          return
        }

        if (index > this.history.length) {
          const conversationId = this.history[this.history.length - 1].conversationId
          this.active = conversationId
          this.reloadRoute(conversationId)
        }
      }
    },

    async setActive(conversationId: string) {
      this.active = conversationId
      return await this.reloadRoute(conversationId)
    },

    getChatByConversationIdAndIndex(conversationId: string, index: number) {
      if (!conversationId || conversationId === '') {
        if (this.chat.length) return this.chat[0].data[index]
        return null
      }
      const chatIndex = this.chat.findIndex(item => item.conversationId === conversationId)
      if (chatIndex !== -1) return this.chat[chatIndex].data[index]
      return null
    },

    addChatByConversationId(conversationId: string, chat: Chat.Chat, isHistory = false) {
      if (!conversationId || conversationId === '') {
        if (this.history.length === 0) {
          const conversationId = Date.now().toString()
          this.history.push({ conversationId, title: chat.text, isEdit: false })
          this.chat.push({ conversationId, data: [chat] })
          this.active = conversationId
          this.recordState()
        } else {
          this.chat[0].data.push(chat)
          // if (this.history[0].title === t('chat.newChatTitle')) this.history[0].title = chat.text
          if (!isHistory) this.moveToTop(this.chat[0].conversationId)
        }
      } else {
        const index = this.chat.findIndex(item => item.conversationId === conversationId)
        if (index !== -1) {
          this.chat[index].data.push(chat)
          // if (this.history[index].title === t('chat.newChatTitle'))
          //   this.history[index].title = chat.text
          if (!isHistory) this.moveToTop(conversationId)
        }
      }
    },

    prependChatByConversationId(conversationId: string, chat: Chat.Chat) {
      if (!conversationId || conversationId === '') {
        if (this.history.length === 0) {
          const conversationId = Date.now().toString()
          this.history.push({ conversationId, title: chat.text, isEdit: false })
          this.chat.push({ conversationId, data: [chat] })
          this.active = conversationId
          this.recordState()
        } else {
          this.chat[0].data.unshift(chat)
          this.recordState()
        }
      } else {
        const index = this.chat.findIndex(item => item.conversationId === conversationId)
        if (index !== -1) {
          this.chat[index].data.unshift(chat)
          this.recordState()
        }
      }
    },

    updateChatByConversationId(conversationId: string, index: number, chat: Chat.Chat) {
      if (!conversationId || conversationId === '') {
        if (this.chat.length) {
          this.chat[0].data[index] = chat
          this.recordState()
        }
        return
      }

      const chatIndex = this.chat.findIndex(item => item.conversationId === conversationId)
      if (chatIndex !== -1) {
        this.chat[chatIndex].data[index] = chat
        this.recordState()
      }
    },

    updateChatSomeByConversationId(
      conversationId: string,
      index: number,
      chat: Partial<Chat.Chat>,
    ) {
      if (!conversationId || conversationId === '') {
        if (this.chat.length) {
          this.chat[0].data[index] = { ...this.chat[0].data[index], ...chat }
          this.recordState()
        }
        return
      }

      const chatIndex = this.chat.findIndex(item => item.conversationId === conversationId)
      if (chatIndex !== -1) {
        this.chat[chatIndex].data[index] = { ...this.chat[chatIndex].data[index], ...chat }
        this.recordState()
      }
    },

    deleteChatByConversationId(conversationId: string, index: number) {
      if (!conversationId || conversationId === '') {
        if (this.chat.length) {
          this.chat[0].data.splice(index, 1)
          this.recordState()
        }
        return
      }

      const chatIndex = this.chat.findIndex(item => item.conversationId === conversationId)
      if (chatIndex !== -1) {
        this.chat[chatIndex].data.splice(index, 1)
        this.recordState()
      }
    },

    clearChatByConversationId(conversationId: string) {
      if (!conversationId || conversationId === '') {
        if (this.chat.length) {
          this.chat[0].data = []
          this.recordState()
        }
        return
      }

      const index = this.chat.findIndex(item => item.conversationId === conversationId)
      if (index !== -1) {
        this.chat[index].data = []
        this.recordState()
      }
    },

    clearHistory() {
      // 保存当前的useSystemMessage状态
      const currentUseSystemMessage = this.useSystemMessage

      // 重置状态
      this.$state = {
        ...defaultState(),
        // 但保留useSystemMessage的值
        useSystemMessage: currentUseSystemMessage,
      }

      // 记录状态
      this.recordState()
    },

    clearHistoryOnly() {
      // 只清除history，保留chat和其他设置
      this.history = []
      this.recordState()
    },

    async reloadRoute(conversationId?: string) {
      console.log('reloadRoute', conversationId)
      this.recordState()

      // 检查当前URL是否包含public路径
      // 这里不要用route
      const currentPath = window.location.hash
      // console.log('currentPath', currentPath)
      const isPublicChat = currentPath.includes('/chat/public/')
      const releaseId = currentPath.split('=')[1]
      console.log()

      if (isPublicChat) {
        await router.push({ name: 'public-chat', params: { conversationId }, query: { releaseId } })
      } else {
        await router.push({ name: 'Chat', params: { conversationId } })
      }
    },

    recordState() {
      setLocalState(this.$state)
    },

    moveToTop(conversationId: string) {
      const historyIndex = this.history.findIndex(item => item.conversationId === conversationId)
      const chatIndex = this.chat.findIndex(item => item.conversationId === conversationId)

      if (historyIndex !== -1 && chatIndex !== -1) {
        // 从数组中移除项目
        const historyItem = this.history.splice(historyIndex, 1)[0]
        const chatItem = this.chat.splice(chatIndex, 1)[0]

        // 添加到数组开头
        this.history.unshift(historyItem)
        this.chat.unshift(chatItem)

        this.recordState()
      }
    },
  },
})
